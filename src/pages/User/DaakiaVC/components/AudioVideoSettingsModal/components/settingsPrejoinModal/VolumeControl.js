import React from 'react';
import { Slider } from 'antd';
import { ReactComponent as SpeakerIcon } from '../../Assets/speaker.svg';
import { ReactComponent as SpeakerFlowIcon } from '../../Assets/speakerFlow.svg';

/**
 * VolumeControl Component
 * Volume slider with speaker icons
 * @param {number} value - Current volume value (0-100)
 * @param {function} onChange - Volume change handler
 * @param {number} min - Minimum volume value
 * @param {number} max - Maximum volume value
 */
function VolumeControl({ 
  value = 100, 
  onChange, 
  min = 0, 
  max = 100 
}) {
  return (
    <div className="volume-control">
      <SpeakerIcon className="slider-icon" style={{ marginRight: '12px' }} />
      <Slider
        value={value}
        onChange={onChange}
        min={min}
        max={max}
        tooltip={{ formatter: (value) => `${value}%` }}
      />
      <SpeakerFlowIcon className="slider-icon" style={{ marginLeft: '12px' }} />
    </div>
  );
}

export default VolumeControl;
